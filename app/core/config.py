"""Application configuration"""

from pathlib import Path
from typing import Optional, List

from pydantic import Field, field_validator
from pydantic_settings import BaseSettings, SettingsConfigDict


class Settings(BaseSettings):
    """Application settings"""
    
    model_config = SettingsConfigDict(
        env_file=".env",
        env_file_encoding="utf-8",
        case_sensitive=False
    )
    
    # Database
    database_url: str = Field(default="sqlite:///./pandoc_api.db")
    
    # Storage paths
    upload_dir: Path = Field(default=Path("storage/uploads"))
    output_dir: Path = Field(default=Path("storage/outputs"))
    template_dir: Path = Field(default=Path("templates"))

    # Additional template folders (comma-separated paths)
    additional_template_dirs: str = Field(default="")

    @field_validator('additional_template_dirs')
    @classmethod
    def validate_additional_template_dirs(cls, v: str) -> str:
        """Validate additional template directories"""
        if not v:
            return v

        # Split by comma and validate each path
        paths = [p.strip() for p in v.split(',') if p.strip()]
        for path_str in paths:
            path = Path(path_str)
            if not path.exists():
                raise ValueError(f"Additional template directory does not exist: {path_str}")

        return v

    def get_additional_template_dirs(self) -> List[Path]:
        """Get list of additional template directories as Path objects"""
        if not self.additional_template_dirs:
            return []

        paths = [p.strip() for p in self.additional_template_dirs.split(',') if p.strip()]
        return [Path(p) for p in paths]
    
    # API settings
    api_host: str = Field(default="0.0.0.0")
    api_port: int = Field(default=8000)
    debug: bool = Field(default=False)
    
    # Pandoc settings
    pandoc_timeout: int = Field(default=300)  # 5 minutes
    max_file_size: int = Field(default=100 * 1024 * 1024)  # 100MB
    
    # Security
    secret_key: str = Field(default="your-secret-key-here")
    
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        # Ensure directories exist
        self.upload_dir.mkdir(parents=True, exist_ok=True)
        self.output_dir.mkdir(parents=True, exist_ok=True)
        self.template_dir.mkdir(parents=True, exist_ok=True)


# Global settings instance
settings = Settings()
